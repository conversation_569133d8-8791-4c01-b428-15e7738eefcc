const Content = require('../models/Content');
const Bid = require('../models/Bid');
const Order = require('../models/Order');
const User = require('../models/User');
const Setting = require('../models/Setting');
const Notification = require('../models/Notification');
const { sendEmail } = require('../utils/sendEmail');
const { generateBidAcceptanceEmail } = require('../utils/emailTemplates');
const { getPaymentDeadline } = require('../config/timeouts');

class AuctionConversionJob {
    constructor() {
        this.jobName = 'auctionConversion';
    }

    async run() {
        console.log('🔄 Starting auction conversion job...');

        try {
            // Use UTC date for comparison
            const currentTime = new Date();

            // Find all auction content that has ended but hasn't been processed
            const expiredAuctions = await Content.find({
                saleType: { $in: ['Auction'] },
                'auctionDetails.auctionEndDate': {
                    $lt: currentTime // MongoDB automatically handles UTC conversion
                },
                auctionStatus: { $ne: 'Ended' },
                isSold: false,
                status: 'Published'
            }).populate('seller', 'firstName lastName email');

            console.log(`📊 Found ${expiredAuctions.length} expired auctions to check`);

            let conversionsCount = 0;
            let autoResolvedCount = 0;

            for (const auction of expiredAuctions) {
                try {
                    const result = await this.processExpiredAuction(auction, currentTime);
                    if (result.type === 'conversion') {
                        conversionsCount++;
                    } else if (result.type === 'auto-resolved') {
                        autoResolvedCount++;
                    }
                } catch (error) {
                    console.error(`❌ Error processing auction ${auction._id}:`, error);
                    // Continue with other auctions
                }
            }

            const result = {
                totalChecked: expiredAuctions.length,
                conversions: conversionsCount,
                autoResolved: autoResolvedCount,
                timestamp: currentTime.toISOString()
            };

            console.log(`✅ Auction conversion job completed: ${conversionsCount} conversions, ${autoResolvedCount} auto-resolved from ${expiredAuctions.length} expired auctions`);
            return result;

        } catch (error) {
            console.error('❌ Auction conversion job failed:', error);
            throw error;
        }
    }

    async processExpiredAuction(auction, currentTime) {
        // Check if there are any active bids for this auction
        const activeBids = await Bid.find({
            content: auction._id,
            status: 'Active'
        }).sort('-amount').populate('bidder', 'firstName lastName email');

        if (activeBids.length === 0) {
            // No active bids - convert to Fixed Price
            return await this.convertToFixedPrice(auction, currentTime);
        } else {
            // Has bids - automatically resolve auction
            return await this.autoResolveAuction(auction, activeBids, currentTime);
        }
    }

    async convertToFixedPrice(auction, currentTime) {
        const conversionPrice = auction.auctionDetails.basePrice || auction.price || 0;

        await Content.findByIdAndUpdate(auction._id, {
            saleType: 'Fixed',
            price: conversionPrice,
            auctionStatus: 'Ended',
            auctionEndedAt: currentTime,
            'auctionDetails.endTime': currentTime
        });

        console.log(`✅ Converted auction "${auction.title}" to Fixed Price ($${conversionPrice})`);

        // Send notification to seller about conversion
        await this.sendConversionNotification(auction, conversionPrice);

        return { type: 'conversion', price: conversionPrice };
    }

    async autoResolveAuction(auction, activeBids, currentTime) {
        const highestBid = activeBids[0]; // Already sorted by amount descending

        // Check if highest bid meets reserve price (if set)
        if (auction.auctionDetails.reservePrice &&
            highestBid.amount < auction.auctionDetails.reservePrice) {

            // Reserve price not met - mark all bids as lost and convert to fixed price
            await Bid.updateMany(
                { content: auction._id, status: 'Active' },
                { status: 'Lost' }
            );

            console.log(`❌ Auction "${auction.title}" reserve price not met. Highest bid: $${highestBid.amount}, Reserve: $${auction.auctionDetails.reservePrice}`);

            // Send notifications about reserve not met
            await this.sendReserveNotMetNotifications(auction, highestBid, activeBids);

            // Convert to fixed price at reserve price
            await Content.findByIdAndUpdate(auction._id, {
                saleType: 'Fixed',
                price: auction.auctionDetails.reservePrice,
                auctionStatus: 'Ended',
                auctionEndedAt: currentTime,
                'auctionDetails.endTime': currentTime
            });

            return { type: 'conversion', price: auction.auctionDetails.reservePrice, reason: 'reserve-not-met' };
        }

        // Reserve price met or no reserve price - automatically resolve auction
        return await this.createWinningOrder(auction, highestBid, activeBids, currentTime);
    }

    async createWinningOrder(auction, winningBid, allBids, currentTime) {
        try {
            // Get database settings for fee calculation
            const settings = await Setting.getSingleton();
            const feeBreakdown = Setting.calculateFeeBreakdown(winningBid.amount, settings);

            // Create order for winning bidder
            const order = await Order.create({
                buyer: winningBid.bidder._id,
                seller: auction.seller._id,
                content: auction._id,
                orderType: "Auction",
                amount: winningBid.amount,
                platformFee: feeBreakdown.platformCommission,
                sellerEarnings: feeBreakdown.finalSellerEarnings,
                totalAmount: winningBid.amount,
                bidId: winningBid._id,
                paymentDeadline: getPaymentDeadline(),
                status: "Pending",
            });

            // Update winning bid status
            winningBid.status = "Won";
            await winningBid.save();

            // Update all other bids to Lost
            await Bid.updateMany(
                { content: auction._id, status: "Active", _id: { $ne: winningBid._id } },
                { status: "Lost" }
            );

            // Update auction content
            await Content.findByIdAndUpdate(auction._id, {
                auctionStatus: "Ended",
                auctionEndedAt: currentTime,
                winningBidId: winningBid._id,
                isSold: true,
                soldAt: currentTime,
                'auctionDetails.endTime': currentTime
            });

            console.log(`🎉 Auto-resolved auction "${auction.title}" - Winner: ${winningBid.bidder.firstName} ${winningBid.bidder.lastName} ($${winningBid.amount})`);

            // Send notifications
            await this.sendWinnerNotifications(auction, winningBid, order);
            await this.sendLoserNotifications(auction, allBids.slice(1)); // All except winner
            await this.sendSellerNotification(auction, winningBid, order);

            return {
                type: 'auto-resolved',
                winningBid: winningBid.amount,
                winner: winningBid.bidder.email,
                orderId: order._id
            };

        } catch (error) {
            console.error(`❌ Error creating winning order for auction ${auction._id}:`, error);
            throw error;
        }
    }

    async sendWinnerNotifications(auction, winningBid, order) {
        try {
            // Create in-app notification
            await Notification.create({
                user: winningBid.bidder._id,
                title: '🎉 Congratulations! You won the auction!',
                message: `Your bid of $${winningBid.amount} for "${auction.title}" was the winning bid. Complete your payment to access the content.`,
                type: 'bid',
                relatedId: order._id,
                onModel: 'Order'
            });

            // Send email notification
            const checkoutUrl = `${process.env.FRONTEND_URL}/checkout/${order._id}`;
            const emailData = generateBidAcceptanceEmail({
                bidder: winningBid.bidder,
                content: {
                    title: auction.title,
                    sport: auction.sport,
                    contentType: auction.contentType,
                    seller: auction.seller,
                },
                bid: winningBid,
                checkoutUrl: checkoutUrl,
                isAutoResolved: true
            });

            // Send email asynchronously
            sendEmail({
                email: winningBid.bidder.email,
                subject: emailData.subject,
                message: emailData.message,
                html: emailData.html,
            }).then(() => {
                console.log(`✅ Winner notification email sent to ${winningBid.bidder.email}`);
            }).catch((emailError) => {
                console.error("❌ Error sending winner notification email:", emailError);
            });

        } catch (error) {
            console.error('❌ Error sending winner notifications:', error);
        }
    }

    async sendLoserNotifications(auction, losingBids) {
        try {
            for (const bid of losingBids) {
                // Create in-app notification
                await Notification.create({
                    user: bid.bidder._id,
                    title: 'Auction Ended',
                    message: `The auction for "${auction.title}" has ended. Unfortunately, your bid was not the highest.`,
                    type: 'bid',
                    relatedId: auction._id,
                    onModel: 'Content'
                });

                // Send email notification asynchronously
                const emailSubject = `Auction Ended - ${auction.title}`;
                const emailMessage = `
Hello ${bid.bidder.firstName},

The auction for "${auction.title}" has ended. Unfortunately, your bid of $${bid.amount} was not the winning bid.

Thank you for participating! There are many other great content and auctions available on XO Sports Hub.

Best regards,
The XO Sports Hub Team
                `;

                sendEmail({
                    email: bid.bidder.email,
                    subject: emailSubject,
                    message: emailMessage,
                }).then(() => {
                    console.log(`✅ Loser notification sent to ${bid.bidder.email}`);
                }).catch((emailError) => {
                    console.error(`❌ Error sending loser notification to ${bid.bidder.email}:`, emailError);
                });
            }
        } catch (error) {
            console.error('❌ Error sending loser notifications:', error);
        }
    }

    async sendSellerNotification(auction, winningBid, order) {
        try {
            // Create in-app notification
            await Notification.create({
                user: auction.seller._id,
                title: '🎉 Your auction has been resolved!',
                message: `Your auction for "${auction.title}" ended with a winning bid of $${winningBid.amount}. The buyer has been notified to complete payment.`,
                type: 'bid',
                relatedId: order._id,
                onModel: 'Order'
            });

            // Send email notification
            const emailSubject = `🎉 Your auction has been resolved - ${auction.title}`;
            const emailMessage = `
Hello ${auction.seller.firstName},

Great news! Your auction for "${auction.title}" has automatically ended with a winning bid.

Auction Details:
- Winning Bid: $${winningBid.amount}
- Winner: ${winningBid.bidder.firstName} ${winningBid.bidder.lastName}
- Your Earnings: $${order.sellerEarnings} (after platform fees)

The winning bidder has been notified and has ${Math.ceil((order.paymentDeadline - new Date()) / (1000 * 60 * 60))} hours to complete their payment.

You'll receive another notification once the payment is completed.

Best regards,
The XO Sports Hub Team
            `;

            sendEmail({
                email: auction.seller.email,
                subject: emailSubject,
                message: emailMessage,
            }).then(() => {
                console.log(`✅ Seller notification sent to ${auction.seller.email}`);
            }).catch((emailError) => {
                console.error(`❌ Error sending seller notification to ${auction.seller.email}:`, emailError);
            });

        } catch (error) {
            console.error('❌ Error sending seller notification:', error);
        }
    }

    async sendConversionNotification(auction, conversionPrice) {
        try {
            // Create in-app notification
            await Notification.create({
                user: auction.seller._id,
                title: 'Auction Converted to Fixed Price',
                message: `Your auction for "${auction.title}" ended with no bids and has been converted to a fixed-price listing at $${conversionPrice}.`,
                type: 'account',
                relatedId: auction._id,
                onModel: 'Content'
            });

            // Send email notification
            const emailSubject = `Auction Converted - ${auction.title}`;
            const emailMessage = `
Hello ${auction.seller.firstName},

Your auction for "${auction.title}" has ended with no bids received.

To keep your content available for purchase, we've automatically converted it to a fixed-price listing at $${conversionPrice} (your base price).

Your content is still live and available for buyers to purchase at the fixed price.

Best regards,
The XO Sports Hub Team
            `;

            sendEmail({
                email: auction.seller.email,
                subject: emailSubject,
                message: emailMessage,
            }).then(() => {
                console.log(`✅ Conversion notification sent to ${auction.seller.email}`);
            }).catch((emailError) => {
                console.error(`❌ Error sending conversion notification to ${auction.seller.email}:`, emailError);
            });

        } catch (error) {
            console.error('❌ Error sending conversion notification:', error);
        }
    }

    async sendReserveNotMetNotifications(auction, highestBid, allBids) {
        try {
            // Notify all bidders that reserve was not met
            for (const bid of allBids) {
                await Notification.create({
                    user: bid.bidder._id,
                    title: 'Auction Ended - Reserve Not Met',
                    message: `The auction for "${auction.title}" ended but the reserve price was not met. The item is now available at a fixed price.`,
                    type: 'bid',
                    relatedId: auction._id,
                    onModel: 'Content'
                });

                const emailSubject = `Auction Ended - Reserve Not Met - ${auction.title}`;
                const emailMessage = `
Hello ${bid.bidder.firstName},

The auction for "${auction.title}" has ended, but unfortunately the reserve price of $${auction.auctionDetails.reservePrice} was not met.

The highest bid was $${highestBid.amount}, which was below the seller's reserve price.

The item is now available for purchase at a fixed price. You can still purchase it if you're interested.

Thank you for participating!

Best regards,
The XO Sports Hub Team
                `;

                sendEmail({
                    email: bid.bidder.email,
                    subject: emailSubject,
                    message: emailMessage,
                }).catch((emailError) => {
                    console.error(`❌ Error sending reserve not met notification to ${bid.bidder.email}:`, emailError);
                });
            }

            // Notify seller
            await Notification.create({
                user: auction.seller._id,
                title: 'Auction Ended - Reserve Not Met',
                message: `Your auction for "${auction.title}" ended but the reserve price of $${auction.auctionDetails.reservePrice} was not met. It's now listed at fixed price.`,
                type: 'account',
                relatedId: auction._id,
                onModel: 'Content'
            });

        } catch (error) {
            console.error('❌ Error sending reserve not met notifications:', error);
        }
    }
}

module.exports = new AuctionConversionJob(); 