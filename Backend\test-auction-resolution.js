/**
 * Test script for auction resolution functionality
 * This script tests the automatic auction resolution logic
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models and job
const Content = require('./models/Content');
const Bid = require('./models/Bid');
const User = require('./models/User');
const auctionConversionJob = require('./jobs/auctionConversion');

async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to MongoDB');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
        process.exit(1);
    }
}

async function testAuctionResolution() {
    console.log('🧪 Testing auction resolution logic...\n');

    try {
        // Find some test auctions that have ended
        const expiredAuctions = await Content.find({
            saleType: 'Auction',
            'auctionDetails.auctionEndDate': { $lt: new Date() },
            auctionStatus: { $ne: 'Ended' },
            isSold: false,
            status: 'Published'
        }).limit(3);

        console.log(`📊 Found ${expiredAuctions.length} expired auctions for testing`);

        if (expiredAuctions.length === 0) {
            console.log('ℹ️  No expired auctions found. Creating a test scenario...');
            await createTestAuction();
            return;
        }

        // Show auction details before processing
        for (const auction of expiredAuctions) {
            const bids = await Bid.find({ content: auction._id, status: 'Active' })
                .populate('bidder', 'firstName lastName email')
                .sort('-amount');
            
            console.log(`\n📋 Auction: ${auction.title}`);
            console.log(`   End Date: ${auction.auctionDetails.auctionEndDate}`);
            console.log(`   Status: ${auction.auctionStatus}`);
            console.log(`   Active Bids: ${bids.length}`);
            
            if (bids.length > 0) {
                console.log(`   Highest Bid: $${bids[0].amount} by ${bids[0].bidder.firstName} ${bids[0].bidder.lastName}`);
                if (auction.auctionDetails.reservePrice) {
                    console.log(`   Reserve Price: $${auction.auctionDetails.reservePrice}`);
                    console.log(`   Reserve Met: ${bids[0].amount >= auction.auctionDetails.reservePrice ? 'Yes' : 'No'}`);
                }
            }
        }

        console.log('\n🔄 Running auction conversion job...\n');

        // Run the auction conversion job
        const result = await auctionConversionJob.run();

        console.log('\n📊 Job Results:');
        console.log(`   Total Checked: ${result.totalChecked}`);
        console.log(`   Conversions: ${result.conversions}`);
        console.log(`   Auto-Resolved: ${result.autoResolved}`);
        console.log(`   Timestamp: ${result.timestamp}`);

        console.log('\n✅ Test completed successfully!');

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

async function createTestAuction() {
    console.log('🏗️  Creating test auction scenario...');
    
    // This would create test data if needed
    // For now, just log that no test data was found
    console.log('ℹ️  No test auctions created. Please create some expired auctions manually for testing.');
}

async function main() {
    await connectDB();
    await testAuctionResolution();
    
    console.log('\n🔚 Closing database connection...');
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
    console.error('❌ Unhandled Promise Rejection:', err);
    process.exit(1);
});

// Run the test
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testAuctionResolution };
